version: "3.8"

services:
  # PostgreSQL 主数据库
  postgresql:
    image: postgres:15-alpine
    container_name: chaiguanjia_postgresql
    environment:
      POSTGRES_DB: ${POSTGRES_DB:-chaiguanjia}
      POSTGRES_USER: ${POSTGRES_USER:-admin}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD:-chaiguanjia2024}
      POSTGRES_INITDB_ARGS: "--encoding=UTF-8 --locale=C"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./database/init:/docker-entrypoint-initdb.d:ro
    ports:
      - "${POSTGRES_PORT:-5432}:5432"
    networks:
      - chaiguanjia_network
    healthcheck:
      test:
        [
          "CMD-SHELL",
          "pg_isready -U ${POSTGRES_USER:-admin} -d ${POSTGRES_DB:-chaiguanjia}",
        ]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    restart: unless-stopped

  # Redis 缓存系统
  redis:
    image: redis:7-alpine
    container_name: chaiguanjia_redis
    command: redis-server --appendonly yes --requirepass ${REDIS_PASSWORD:-chaiguanjia2024}
    volumes:
      - redis_data:/data
      - ./infrastructure/redis/redis.conf:/usr/local/etc/redis/redis.conf:ro
    ports:
      - "${REDIS_PORT:-6379}:6379"
    networks:
      - chaiguanjia_network
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 20s
    restart: unless-stopped

  # RabbitMQ 消息队列
  rabbitmq:
    image: rabbitmq:3.12-management-alpine
    container_name: chaiguanjia_rabbitmq
    environment:
      RABBITMQ_DEFAULT_USER: ${RABBITMQ_USER:-admin}
      RABBITMQ_DEFAULT_PASS: ${RABBITMQ_PASSWORD:-chaiguanjia2024}
      RABBITMQ_DEFAULT_VHOST: ${RABBITMQ_VHOST:-chaiguanjia}
    volumes:
      - rabbitmq_data:/var/lib/rabbitmq
    ports:
      - "${RABBITMQ_PORT:-5672}:5672"
      - "${RABBITMQ_MANAGEMENT_PORT:-15672}:15672"
    networks:
      - chaiguanjia_network
    healthcheck:
      test: ["CMD", "rabbitmqctl", "node_health_check"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
    restart: unless-stopped

  # Elasticsearch 搜索引擎
  elasticsearch:
    image: elasticsearch:8.10.4
    container_name: chaiguanjia_elasticsearch
    environment:
      - node.name=chaiguanjia-es-node
      - cluster.name=chaiguanjia-cluster
      - discovery.type=single-node
      - bootstrap.memory_lock=true
      - "ES_JAVA_OPTS=-Xms512m -Xmx512m"
      - xpack.security.enabled=false
      - xpack.security.enrollment.enabled=false
      - http.cors.enabled=true
      - http.cors.allow-origin="*"
    ulimits:
      memlock:
        soft: -1
        hard: -1
    volumes:
      - es_data:/usr/share/elasticsearch/data
    ports:
      - "${ELASTICSEARCH_PORT:-9200}:9200"
      - "${ELASTICSEARCH_TRANSPORT_PORT:-9300}:9300"
    networks:
      - chaiguanjia_network
    healthcheck:
      test:
        ["CMD-SHELL", "curl -f http://localhost:9200/_cluster/health || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
    restart: unless-stopped

  # MinIO 对象存储
  minio:
    image: minio/minio:latest
    container_name: chaiguanjia_minio
    command: server /data --console-address ":9001"
    environment:
      - MINIO_ROOT_USER=${MINIO_ACCESS_KEY:-chaiguanjia_admin}
      - MINIO_ROOT_PASSWORD=${MINIO_SECRET_KEY:-chaiguanjia2024_secret}
      - MINIO_REGION=${MINIO_REGION:-us-east-1}
    volumes:
      - minio_data:/data
    ports:
      - "${MINIO_PORT:-9000}:9000"
      - "${MINIO_CONSOLE_PORT:-9001}:9001"
    networks:
      - chaiguanjia_network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:9000/minio/health/live"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
    restart: unless-stopped

  # Backend API 服务
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
      args:
        - PYTHON_VERSION=${PYTHON_VERSION:-3.11}
        - HTTP_PROXY=http://host.docker.internal:7897
        - HTTPS_PROXY=http://host.docker.internal:7897
    container_name: chaiguanjia_backend
    environment:
      - DATABASE_URL=postgresql://${POSTGRES_USER:-admin}:${POSTGRES_PASSWORD:-chaiguanjia2024}@postgresql:5432/${POSTGRES_DB:-chaiguanjia}
      - REDIS_URL=redis://:${REDIS_PASSWORD:-chaiguanjia2024}@redis:6379/0
      - RABBITMQ_URL=amqp://${RABBITMQ_USER:-admin}:${RABBITMQ_PASSWORD:-chaiguanjia2024}@rabbitmq:5672/${RABBITMQ_VHOST:-chaiguanjia}
      - ELASTICSEARCH_URL=http://elasticsearch:9200
      - MINIO_ENDPOINT=minio:9000
      - MINIO_ACCESS_KEY=${MINIO_ACCESS_KEY:-chaiguanjia_admin}
      - MINIO_SECRET_KEY=${MINIO_SECRET_KEY:-chaiguanjia2024_secret}
      - MINIO_BUCKET_NAME=${MINIO_BUCKET_NAME:-chaiguanjia-files}
      - MINIO_SECURE=${MINIO_SECURE:-false}
      - ENVIRONMENT=${ENVIRONMENT:-development}
      - SECRET_KEY=${SECRET_KEY:-your-secret-key-here}
      - DEBUG=${DEBUG:-true}
    volumes:
      - ./backend:/app
      - backend_logs:/app/logs
    ports:
      - "${BACKEND_PORT:-8000}:8000"
    networks:
      - chaiguanjia_network
    depends_on:
      postgresql:
        condition: service_healthy
      redis:
        condition: service_healthy
      rabbitmq:
        condition: service_healthy
      elasticsearch:
        condition: service_healthy
      minio:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
    restart: unless-stopped

  # Frontend 前端服务
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
      args:
        - NODE_VERSION=${NODE_VERSION:-18}
        # - HTTP_PROXY=http://host.docker.internal:7897
        # - HTTPS_PROXY=http://host.docker.internal:7897
    container_name: chaiguanjia_frontend
    environment:
      - REACT_APP_API_URL=${REACT_APP_API_URL:-http://localhost:8000}
      - REACT_APP_ENVIRONMENT=${ENVIRONMENT:-development}
    ports:
      - "${FRONTEND_PORT:-3000}:80"
    networks:
      - chaiguanjia_network
    depends_on:
      - backend
    healthcheck:
      test:
        [
          "CMD",
          "wget",
          "--quiet",
          "--tries=1",
          "--spider",
          "http://localhost:80/health",
        ]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
    restart: unless-stopped

  # Nginx 反向代理
  nginx:
    image: nginx:alpine
    container_name: chaiguanjia_nginx
    volumes:
      - nginx_logs:/var/log/nginx
      - ./infrastructure/nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./infrastructure/nginx/conf.d/default.conf:/etc/nginx/conf.d/default.conf:ro
    ports:
      - "${NGINX_HTTP_PORT:-80}:80"
      - "${NGINX_HTTPS_PORT:-443}:443"
    networks:
      - chaiguanjia_network
    depends_on:
      - backend
      - frontend
    healthcheck:
      test: ["CMD", "nginx", "-t"]
      interval: 30s
      timeout: 10s
      retries: 3
    restart: unless-stopped

# 数据卷定义
volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local
  rabbitmq_data:
    driver: local
  es_data:
    driver: local
  minio_data:
    driver: local
  backend_logs:
    driver: local
  node_modules:
    driver: local
  nginx_logs:
    driver: local

# 网络定义
networks:
  chaiguanjia_network:
    driver: bridge
    ipam:
      driver: default
      config:
        - subnet: **********/16
