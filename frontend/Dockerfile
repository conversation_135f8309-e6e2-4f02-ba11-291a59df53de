# 前端Dockerfile - 柴管家项目
FROM node:18-alpine

# 设置工作目录
WORKDIR /app

# 复制package文件
COPY package*.json ./

# 配置npm使用代理和国内镜像源
RUN npm config set registry https://registry.npmmirror.com && \
    npm config set proxy http://host.docker.internal:7890 && \
    npm config set https-proxy http://host.docker.internal:7890 && \
    npm install --silent || \
    (npm config delete proxy && npm config delete https-proxy && npm install --silent)

# 复制源代码
COPY . .

# 构建生产版本
RUN npm run build

# 使用nginx提供静态文件
FROM nginx:alpine
COPY --from=0 /app/build /usr/share/nginx/html
COPY nginx.conf /etc/nginx/nginx.conf

# 暴露端口
EXPOSE 80

# 启动nginx
CMD ["nginx", "-g", "daemon off;"]
