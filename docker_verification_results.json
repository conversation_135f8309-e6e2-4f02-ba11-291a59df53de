{"docker_services": {"elasticsearch_status": {"success": true, "details": "运行正常: running", "timestamp": **********.43564}, "nginx_status": {"success": true, "details": "运行正常: running", "timestamp": **********.435662}, "postgresql_status": {"success": true, "details": "运行正常: running", "timestamp": **********.435665}, "rabbitmq_status": {"success": true, "details": "运行正常: running", "timestamp": **********.4356658}, "redis_status": {"success": true, "details": "运行正常: running", "timestamp": **********.435668}}, "postgresql": {"connection": {"success": true, "details": "数据库连接正常", "timestamp": **********.4755032}, "query": {"success": true, "details": "SQL查询正常", "timestamp": **********.519367}, "chinese_support": {"success": true, "details": "中文支持正常", "timestamp": **********.56596}}, "redis": {"connection": {"success": true, "details": "Redis连接正常", "timestamp": **********.619394}, "read_write": {"success": true, "details": "读写操作正常", "timestamp": **********.725461}, "chinese_support": {"success": true, "details": "中文支持正常", "timestamp": **********.88364}}, "rabbitmq": {"status": {"success": true, "details": "RabbitMQ状态正常", "timestamp": **********.2171872}, "vhost": {"success": true, "details": "虚拟主机配置正常", "timestamp": **********.4507918}, "user": {"success": true, "details": "用户配置正常", "timestamp": **********.701356}}, "elasticsearch": {"health": {"success": true, "details": "集群状态: green", "timestamp": **********.760503}, "index_create": {"success": true, "details": "索引创建正常", "timestamp": **********.9408152}}, "nginx": {"config": {"success": true, "details": "配置文件正常", "timestamp": **********.0900311}, "process": {"success": true, "details": "Nginx进程运行正常", "timestamp": **********.1420228}}}